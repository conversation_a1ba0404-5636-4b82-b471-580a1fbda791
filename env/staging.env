STAGE=staging
PORT=3000
INTERNAL_GRPC_PORT=5001
LOG_LEVEL=info
JWT_SECRET=abc
JWT_REFRESH_SECRET=asd
ENCRYPT_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmMW7U1Z64ocVtsA2B+D6\nIGh41AsGZ5hRG7IJYuLbgiQeGDiywB+TSUEIqgjl80H2679gbP5p8dpWzALmPPnN\nRufGjnL5u90itzDgOJ8aIb1ecDQNQA9pMkL/3mZxOG4h9RA73Q6/ssayOoJ/Ok6+\nuyxqiLqNixLX6lL6DAa6UBn0wJATYwiCkA8NsoKR387jUc4OUekC/4gFNnWN1GCg\ndAKlyuY/VMRHQp2SS8Zit4ztdVCJje+4nkvQP+zYLV3VYvJHVCVQ4RuR2jaA/Ss3\nnAyCahXxjXXjjlTV7yQVS95t4hPOju9ti6kng6zOFs9A/yvLq14AxBkQTO+8e0c1\nMQIDAQAB\n-----END PUBLIC KEY-----\n"

# PG config
# ------------------------------------------------------------------
POSTGRES_HOST=$POSTGRES_HOST
POSTGRES_PORT=$POSTGRES_PORT
POSTGRES_USER=$POSTGRES_USER
POSTGRES_PASS=$POSTGRES_PASS
POSTGRES_DB_XBIT=
POSTGRES_SSL_MODE=

# Redis config
# ------------------------------------------------------------------
REDIS_HOST=$REDIS_HOST
REDIS_PORT=$REDIS_PORT
REDIS_PASS=$REDIS_PASS
REDIS_DB=0

# Telegram bot config
# ------------------------------------------------------------------
TELEGRAM_BOT_DOMAIN=staging-api.xbit.live
TELEGRAM_BOT_WEBHOOK=
TELEGRAM_BOT_AUTH_TOKEN=
TELEGRAM_LOGIN_URL=https://staging.xbit.live/telegram-auth

# Google OAuth Configuration
# ------------------------------------------------------------------
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_OIDC=

#Telegram tranding bot config
#-------------------------------------------------------------------
TELEGRAM_BOT_SNIPER_TOKEN=

# Wallet service config
# ------------------------------------------------------------------
WALLET_SERVICE_HOST=***
WALLET_SERVICE_PORT=***
WALLET_SERVICE_APIKEY=***

#ETH
# ------------------------------------------------------------------
INFURA_RPC_URL=***
TRON_GRID_API_KEY=***

#EMQX
EMQX_PROTOCOL=
EMQX_HOST=
EMQX_PORT=
EMQX_USER=
EMQX_PASS=

#XBIT LANDING PAGE
LANDING_PAGE_URL=https://staging.xbit.live/

MEME_BASE_URL=https://staging-api.xbit.live/api/meme/graphql
TRANDING_BASE_URL=https://staging-api.xbit.live/api/trading/query

REFERRAL_ADDRESS=https://t.me/zbit_staging_trading_solana_bot
WEBSTITE_ADDRESS=https://staging.xbit.live/meme/discover

TOKEN_DETAILS=https://staging.xbit.live/meme/token/

ACCESS_TOKEN_EXPIRES_IN=1d
REFRESH_TOKEN_EXPIRES_IN=30d

NATS_URL=
NATS_AUTH_TOKEN=
NATS_USER=
NATS_PASS=

JWT_OIDC=
TURNKEY_API_BASE_URL=https://api.turnkey.com
TURNKEY_API_PRIVATE_KEY=
TURNKEY_API_PUBLIC_KEY=
TURNKEY_ORGANIZATION_ID=

REDIS_CLUSTER_HOST=
REDIS_CLUSTER_PORT=
REDIS_CLUSTER_ENABLE_SSL=
REDIS_CLUSTER_PRIMARY_PASS=
REDIS_CLUSTER_SECONDARY_PASS=

TURNKEY_FAKE_PUBLIC_KEY=033ff0089011ed1b7155d015541e4987fb8382fc560f7ec8f3c267e0886c974f7a
TURNKEY_FAKE_PRIVATE_KEY=a9a0dfb3856a1ec2563c05bbdb788a6061fe80c937cd8d27c93842ea1e5c4951

APPLE_CLIENT_ID=com.xtech.xbitmobile

NATS_USER_STREAM=xbit_user
NATS_SUBJECT_WALLET_CREATE=user.wallet.new
NATS_DEX_USER_STREAM=dex_user
NATS_SUBJECT_DEX_WALLET_CREATE=dex.user.wallet.new
NATS_DEX_USER_INFO_STREAM=user_sync_info_stream
NATS_SUBJECT_DEX_USER_SYNC=dex.user_service.sync_info

SENTRY_DSN_USER=

XBIT_WEB_DOMAINS=
XBIT_WEB_DOMAINS=

import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { NatsService } from '../nats/nats.service';
import { SUBJECTS } from '../nats/nats.subject';
import { UserLoginEvent as NatsUserLoginEvent } from '../nats/nats.event';
import { UserLoginEvent } from './user-login.event';

@Injectable()
export class UserLoginListener {
    constructor(
        private readonly natsService: NatsService,
        @InjectPinoLogger(UserLoginListener.name)
        private readonly logger: PinoLogger,
    ) {}

    @OnEvent('user.login')
    async handleUserLogin(event: UserLoginEvent) {
        try {
            // Convert event to NATS message format
            const natsEvent: NatsUserLoginEvent = {
                userId: event.userId,
                loginMethod: event.loginMethod,
                timestamp: event.timestamp,
                fingerprint: event.fingerprint,
                referrerCode: event.referrerCode,
            };

            // Send to NATS asynchronously
            await this.natsService.publish(SUBJECTS.DEX_USER_LOGIN, natsEvent);

            this.logger.info('User login notification sent to NATS', {
                userId: event.userId,
                loginMethod: event.loginMethod,
            });
        } catch (error) {
            // Log error but don't throw to avoid affecting other listeners
            this.logger.error('Failed to send user login notification to NATS', {
                error: error.message,
                userId: event.userId,
                loginMethod: event.loginMethod,
            });
        }
    }
}

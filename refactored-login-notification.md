# Refactored Login Notification Implementation

## Overview
This document describes the refactored login notification feature that uses EventEmitter pattern instead of direct NATS calls to avoid slowing down the login flow.

## Architecture Changes

### Before (Direct NATS)
```
Login Method → JWT Creation → NATS Publish (blocking) → Return Response
```

### After (EventEmitter Pattern)
```
Login Method → JWT Creation → EventEmitter.emit() (non-blocking) → Return Response
                                      ↓
                              UserLoginListener → NATS Publish (async)
```

## Implementation Details

### 1. Event System
- **Event Class**: `libs/internal/events/user-login.event.ts`
- **Event Listener**: `libs/internal/events/user-login.listener.ts`
- **Events Module**: `libs/internal/events/events.module.ts`

### 2. Login Methods Updated
All login methods now emit `'user.login'` event instead of calling NATS directly:

**WalletAuthService:**
- `login()` - emits event with method `'wallet'`

**TelegramAuthService:**
- `login()` - emits event with method `'telegram'`

**TurnkeyService:**
- `loginWithEmailOtp()` - emits event with method `'email_otp'`
- `loginWithGoogle()` - emits event with method `'google'`
- `loginByWallet()` (deprecated) - emits event with method `'wallet'`
- `loginWithTelegram()` (deprecated) - emits event with method `'telegram'`
- `loginByWalletV2()` - emits event with method `'wallet'`
- `loginWithApple()` - emits event with method `'google'`

### 3. Event Flow
1. User performs login
2. Login method creates JWT tokens
3. Login method emits `UserLoginEvent` using `EventEmitter2`
4. `UserLoginListener` handles the event asynchronously
5. Listener converts event to NATS format and publishes to `dex.user.login`
6. Login response is returned immediately (no waiting for NATS)

### 4. Benefits
- **Faster Login**: No blocking NATS calls in login flow
- **Better Error Handling**: NATS failures don't affect login success
- **Scalability**: Can easily add more listeners for other purposes
- **Separation of Concerns**: Login logic separated from notification logic

### 5. Error Handling
- Login methods complete successfully regardless of event emission
- Event listener catches and logs NATS errors without throwing
- Failed notifications don't impact user experience

### 6. Configuration
Same NATS configuration as before:
- `NATS_DEX_USER_STREAM=dex_user`
- `NATS_SUBJECT_DEX_USER_LOGIN=dex.user.login`

### 7. Testing
Login flow remains the same from user perspective. To test notifications:
```bash
# Subscribe to login events
nats sub dex.user.login

# Perform any login action
# Check for messages in NATS stream
```

### 8. Module Registration
- `EventsModule` added to `AuthModule` and `TurnkeyModule`
- `EventEmitterModule` already registered in main app modules
- `UserLoginListener` automatically registered via `@OnEvent` decorator

### 9. Message Format
```json
{
  "userId": "user-uuid",
  "loginMethod": "wallet|telegram|email_otp|google",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "fingerprint": "optional-fingerprint",
  "referrerCode": "optional-referrer-code"
}
```
